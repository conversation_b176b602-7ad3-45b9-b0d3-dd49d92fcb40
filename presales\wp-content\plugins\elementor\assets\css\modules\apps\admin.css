body.elementor-apps-page {
  background: var(--e-a-color-white);
}

.e-a-apps .e-a-page-title {
  margin: 30px auto 60px;
  max-width: 770px;
  text-align: center;
}
.e-a-apps .e-a-page-title h2 {
  font-size: 28px;
  margin: 0;
  line-height: 1.6;
}
.e-a-apps .e-a-page-title p {
  margin-block-start: 0;
  font-size: 16px;
}
.e-a-apps .e-a-page-title p a {
  color: inherit;
}
.e-a-apps .e-a-page-footer {
  margin: 60px auto;
  text-align: center;
}
.e-a-apps .e-a-page-footer p {
  max-width: 1200px;
  margin: auto;
}
.e-a-apps .e-a-list {
  display: grid;
  grid-gap: 30px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}
.e-a-apps .e-a-item {
  border: var(--e-a-border);
  border-radius: var(--e-a-border-radius);
  display: flex;
  padding: 20px 24px;
  flex-direction: column;
  align-items: flex-start;
  transition: var(--e-a-transition-hover);
}
.e-a-apps .e-a-item:hover {
  border-color: var(--e-a-border-color-bold);
}
.e-a-apps .e-a-heading {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}
.e-a-apps .e-a-heading .e-a-img {
  border-radius: var(--e-a-border-radius);
  width: 70px;
  display: flex;
  margin-block-end: 20px;
}
.e-a-apps .e-a-heading .e-a-badge {
  background: #ECFDF5;
  color: #047857;
  border-radius: 100px;
  padding: 3px 8px;
}
.e-a-apps .e-a-title,
.e-a-apps .e-a-author {
  margin: 0;
  line-height: 1.6;
}
.e-a-apps .e-a-author {
  font-size: 12px;
}
.e-a-apps .e-a-author a {
  color: inherit;
}
.e-a-apps .e-a-desc {
  flex-grow: 1;
}
.e-a-apps .e-a-offering {
  font-size: 12px;
  font-style: italic;
}
.e-a-apps .e-a-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-block-start: 20px;
}
.e-a-apps .e-a-actions a {
  text-decoration: none;
}
.e-a-apps .e-a-actions .e-accent {
  margin-inline-start: auto;
}
.e-a-apps .e-a-actions .e-a-learn-more {
  color: #4338CA;
  font-weight: 500;
}

/*# sourceMappingURL=admin.css.map */