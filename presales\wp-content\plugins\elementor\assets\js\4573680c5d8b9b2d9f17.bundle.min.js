/*! elementor - v3.29.0 - 04-06-2025 */
(self.webpackChunkelementor=self.webpackChunkelementor||[]).push([[4527],{84527:(e,t,r)=>{"use strict";var o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(r(40989)),s=o(r(39805)),u=o(r(26153));t.default=(0,n.default)((function Module(){(0,s.default)(this,Module),elementor.elementsManager.registerElementType(new u.default)}))},26153:(e,t,r)=>{"use strict";var o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=t.NestedTabs=void 0;var n=o(r(39805)),s=o(r(40989)),u=o(r(15118)),a=o(r(29402)),i=o(r(87861)),l=o(r(6089));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}var f=t.NestedTabs=function(e){function NestedTabs(){return(0,n.default)(this,NestedTabs),function _callSuper(e,t,r){return t=(0,a.default)(t),(0,u.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,a.default)(e).constructor):t.apply(e,r))}(this,NestedTabs,arguments)}return(0,i.default)(NestedTabs,e),(0,s.default)(NestedTabs,[{key:"getType",value:function getType(){return"nested-tabs"}},{key:"getView",value:function getView(){return l.default}}])}(elementor.modules.elements.types.NestedElementBase);t.default=f},6089:(e,t,r)=>{"use strict";var o=r(96784);Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var n=o(r(39805)),s=o(r(40989)),u=o(r(15118)),a=o(r(29402)),i=o(r(87861));function _isNativeReflectConstruct(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){})))}catch(e){}return(_isNativeReflectConstruct=function _isNativeReflectConstruct(){return!!e})()}t.default=function(e){function View(){return(0,n.default)(this,View),function _callSuper(e,t,r){return t=(0,a.default)(t),(0,u.default)(e,_isNativeReflectConstruct()?Reflect.construct(t,r||[],(0,a.default)(e).constructor):t.apply(e,r))}(this,View,arguments)}return(0,i.default)(View,e),(0,s.default)(View,[{key:"filter",value:function filter(e,t){return e.attributes.dataIndex=t+1,!0}},{key:"onAddChild",value:function onAddChild(e){var t,r,o=null===(t=e._parent.$el.find(".e-n-tabs")[0])||void 0===t?void 0:t.dataset.widgetNumber,n=e.model.attributes.dataIndex,s=null===(r=e._parent.$el.find('.e-n-tab-title[data-tab-index="'.concat(n,'"]')))||void 0===r?void 0:r.attr("id");e.$el.attr({id:"e-n-tab-content-"+o+n,role:"tabpanel","aria-labelledby":s,"data-tab-index":n,style:"--n-tabs-title-order: "+n+";"}),elementor.previewView.isBuffering&&1===n&&e.$el.addClass("e-active")}}])}($e.components.get("nested-elements").exports.NestedView)},36417:e=>{e.exports=function _assertThisInitialized(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e},e.exports.__esModule=!0,e.exports.default=e.exports},39805:e=>{e.exports=function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")},e.exports.__esModule=!0,e.exports.default=e.exports},40989:(e,t,r)=>{var o=r(45498);function _defineProperties(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,o(n.key),n)}}e.exports=function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e},e.exports.__esModule=!0,e.exports.default=e.exports},29402:e=>{function _getPrototypeOf(t){return e.exports=_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},e.exports.__esModule=!0,e.exports.default=e.exports,_getPrototypeOf(t)}e.exports=_getPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},87861:(e,t,r)=>{var o=r(91270);e.exports=function _inherits(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&o(e,t)},e.exports.__esModule=!0,e.exports.default=e.exports},15118:(e,t,r)=>{var o=r(10564).default,n=r(36417);e.exports=function _possibleConstructorReturn(e,t){if(t&&("object"==o(t)||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return n(e)},e.exports.__esModule=!0,e.exports.default=e.exports},91270:e=>{function _setPrototypeOf(t,r){return e.exports=_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},e.exports.__esModule=!0,e.exports.default=e.exports,_setPrototypeOf(t,r)}e.exports=_setPrototypeOf,e.exports.__esModule=!0,e.exports.default=e.exports},11327:(e,t,r)=>{var o=r(10564).default;e.exports=function toPrimitive(e,t){if("object"!=o(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)},e.exports.__esModule=!0,e.exports.default=e.exports},45498:(e,t,r)=>{var o=r(10564).default,n=r(11327);e.exports=function toPropertyKey(e){var t=n(e,"string");return"symbol"==o(t)?t:t+""},e.exports.__esModule=!0,e.exports.default=e.exports}}]);