/*! elementor - v3.29.0 - 04-06-2025 */
"use strict";
(self["webpackChunkelementorFrontend"] = self["webpackChunkelementorFrontend"] || []).push([["image-carousel"],{

/***/ "../assets/dev/js/frontend/handlers/image-carousel.js":
/*!************************************************************!*\
  !*** ../assets/dev/js/frontend/handlers/image-carousel.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {



Object.defineProperty(exports, "__esModule", ({
  value: true
}));
exports["default"] = void 0;
class ImageCarousel extends elementorModules.frontend.handlers.CarouselBase {
  getDefaultSettings() {
    const settings = super.getDefaultSettings();
    settings.selectors.carousel = '.elementor-image-carousel-wrapper';
    return settings;
  }
}
exports["default"] = ImageCarousel;

/***/ })

}]);
//# sourceMappingURL=image-carousel.1a3e0c6222562304eed5.bundle.js.map