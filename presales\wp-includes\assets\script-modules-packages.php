<?php return array('interactivity/index.js' => array('dependencies' => array(), 'version' => '48fc4752aca8f8795ca8', 'type' => 'module'), 'interactivity/debug.js' => array('dependencies' => array(), 'version' => 'beb31ebdbe898d3dd230', 'type' => 'module'), 'interactivity-router/index.js' => array('dependencies' => array('@wordpress/interactivity', array('id' => '@wordpress/a11y', 'import' => 'dynamic')), 'version' => '549bd2787122793df49c', 'type' => 'module'), 'a11y/index.js' => array('dependencies' => array(), 'version' => '2a5dd8e0f11b6868f8cf', 'type' => 'module'), 'block-library/file/view.js' => array('dependencies' => array('@wordpress/interactivity'), 'version' => 'e925ab60cccb6624004c', 'type' => 'module'), 'block-library/form/view.js' => array('dependencies' => array('wp-polyfill'), 'version' => '025c7429344421ccb2ef', 'type' => 'module'), 'block-library/image/view.js' => array('dependencies' => array('@wordpress/interactivity'), 'version' => '23364a7b9437dd6c3319', 'type' => 'module'), 'block-library/navigation/view.js' => array('dependencies' => array('@wordpress/interactivity'), 'version' => '0735c27ca16ce2f60efd', 'type' => 'module'), 'block-library/query/view.js' => array('dependencies' => array('@wordpress/interactivity', array('id' => '@wordpress/interactivity-router', 'import' => 'dynamic')), 'version' => '6ac3e743320307785d41', 'type' => 'module'), 'block-library/search/view.js' => array('dependencies' => array('@wordpress/interactivity'), 'version' => 'e7b1695e621770b7ebb8', 'type' => 'module'));
