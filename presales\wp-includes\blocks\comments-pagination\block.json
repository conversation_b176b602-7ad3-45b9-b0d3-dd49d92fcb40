{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "core/comments-pagination", "title": "Comments Pagination", "category": "theme", "parent": ["core/comments"], "allowedBlocks": ["core/comments-pagination-previous", "core/comments-pagination-numbers", "core/comments-pagination-next"], "description": "Displays a paginated navigation to next/previous set of comments, when applicable.", "textdomain": "default", "attributes": {"paginationArrow": {"type": "string", "default": "none"}}, "example": {"attributes": {"paginationArrow": "none"}}, "providesContext": {"comments/paginationArrow": "paginationArrow"}, "supports": {"align": true, "reusable": false, "html": false, "color": {"gradients": true, "link": true, "__experimentalDefaultControls": {"background": true, "text": true, "link": true}}, "layout": {"allowSwitching": false, "allowInheriting": false, "default": {"type": "flex"}}, "typography": {"fontSize": true, "lineHeight": true, "__experimentalFontFamily": true, "__experimentalFontWeight": true, "__experimentalFontStyle": true, "__experimentalTextTransform": true, "__experimentalTextDecoration": true, "__experimentalLetterSpacing": true, "__experimentalDefaultControls": {"fontSize": true}}, "interactivity": {"clientNavigation": true}}, "editorStyle": "wp-block-comments-pagination-editor", "style": "wp-block-comments-pagination"}