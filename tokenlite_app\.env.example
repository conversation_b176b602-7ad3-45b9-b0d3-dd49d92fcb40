APP_NAME='TokenLite'
APP_VERSION='1.8.0'
APP_ENV=production
APP_KEY=base64:6auZuaf1gaeC9f+5/xKlt5IILY6maQKJKUEhsp3YSZk=
APP_DEBUG=false
APP_URL=http://app.tokenlite.net
FORCE_HTTPS=false

LOG_CHANNEL=stack

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=
DB_DATABASE=
DB_USERNAME=
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=TLS

TRNX_PREFIX=TNX
USER_PREFIX=UD
ADDED_PREFIX=SYS

# Smart Contract Configuration - BSC MAINNET
CONTRACT_ADDRESS=0x0df5ba78886ab07b03021db5d4b5ca7275cfe934
CONTRACT_IMPLEMENTATION_ADDRESS=0x779042c47025872616084c237062cccf99a29947
CONTRACT_NETWORK=bsc
CONTRACT_NETWORK_ID=56
WEB3_RPC_URL=https://bsc-dataseed.binance.org/
CONTRACT_ENABLED=true
CONTRACT_DECIMALS=18
CONTRACT_GAS_LIMIT=100000
CONTRACT_GAS_PRICE=5000000000
CONTRACT_IS_PROXY=true
CONTRACT_PROXY_TYPE=EIP1967
