-- =====================================================
-- CPANEL DATABASE SETUP FOR SMART CONTRACT INTEGRATION
-- =====================================================
-- Run these SQL commands in your cPanel phpMyAdmin
-- =====================================================

-- 1. Add smart contract fields to existing transactions table
ALTER TABLE `transactions` 
ADD COLUMN `contract_address` VARCHAR(255) NULL AFTER `details`,
ADD COLUMN `contract_network` VARCHAR(255) NULL AFTER `contract_address`,
ADD COLUMN `blockchain_hash` VARCHAR(255) NULL AFTER `contract_network`,
ADD COLUMN `blockchain_status` VARCHAR(255) DEFAULT 'pending' AFTER `blockchain_hash`,
ADD COLUMN `blockchain_data` TEXT NULL AFTER `blockchain_status`,
ADD COLUMN `blockchain_sent_at` TIMESTAMP NULL AFTER `blockchain_data`,
ADD COLUMN `blockchain_confirmed_at` TIMESTAMP NULL AFTER `blockchain_sent_at`,
ADD COLUMN `blockchain_confirmations` INT DEFAULT 0 AFTER `blockchain_confirmed_at`,
ADD COLUMN `token_distribution_status` VARCHAR(255) DEFAULT 'pending' AFTER `blockchain_confirmations`,
ADD COLUMN `token_distribution_data` TEXT NULL AFTER `token_distribution_status`;

-- 2. Create smart_contract_logs table
CREATE TABLE `smart_contract_logs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `transaction_id` int(11) NULL,
  `user_id` int(11) NULL,
  `contract_address` varchar(255) NOT NULL,
  `network` varchar(255) NOT NULL,
  `action` varchar(255) NOT NULL,
  `method` varchar(255) NULL,
  `parameters` text NULL,
  `response` text NULL,
  `blockchain_hash` varchar(255) NULL,
  `status` varchar(255) NOT NULL,
  `error_message` text NULL,
  `gas_used` int(11) NULL,
  `gas_price` varchar(255) NULL,
  `sent_at` timestamp NULL,
  `confirmed_at` timestamp NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_transaction_user` (`transaction_id`, `user_id`),
  KEY `idx_contract_network` (`contract_address`, `network`),
  KEY `idx_status_action` (`status`, `action`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. Add smart contract settings to settings table
INSERT INTO `settings` (`field`, `value`, `created_at`, `updated_at`) VALUES
('token_contract_address', '******************************************', NOW(), NOW()),
('token_contract_implementation_address', '0x779042c47025872616084c237062cccf99a29947', NOW(), NOW()),
('token_contract_network', 'bsc', NOW(), NOW()),
('token_contract_network_id', '56', NOW(), NOW()),
('token_contract_enabled', '1', NOW(), NOW()),
('token_contract_decimals', '18', NOW(), NOW()),
('token_contract_auto_distribute', '0', NOW(), NOW()),
('token_contract_gas_limit', '100000', NOW(), NOW()),
('token_contract_gas_price', '5000000000', NOW(), NOW()),
('web3_rpc_url', 'https://bsc-dataseed.binance.org/', NOW(), NOW()),
('token_contract_owner_address', '', NOW(), NOW()),
('token_contract_private_key', '', NOW(), NOW()),
('token_contract_is_proxy', '1', NOW(), NOW()),
('token_contract_proxy_type', 'EIP1967', NOW(), NOW())
ON DUPLICATE KEY UPDATE 
`value` = VALUES(`value`), 
`updated_at` = NOW();

-- =====================================================
-- VERIFICATION QUERIES (Run these to check if setup worked)
-- =====================================================

-- Check if smart contract fields were added to transactions table
DESCRIBE `transactions`;

-- Check if smart_contract_logs table was created
DESCRIBE `smart_contract_logs`;

-- Check if smart contract settings were added
SELECT * FROM `settings` WHERE `field` LIKE 'token_contract%' OR `field` LIKE 'web3%';

-- =====================================================
-- SETUP COMPLETE!
-- =====================================================
