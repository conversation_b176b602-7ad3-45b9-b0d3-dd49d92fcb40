# 🚀 CPANEL DEPLOYMENT GUIDE

## Complete Smart Contract Integration for cPanel Hosting

### 🎯 **DEPLOYMENT OPTIONS FOR CPANEL**

## **Option 1: Web-Based Migration (Recommended)**

### **Step 1: Upload Migration Script**
1. Upload `migrate_smart_contract.php` to your `public_html` folder
2. Edit the file and change the password:
   ```php
   $MIGRATION_PASSWORD = 'your_secure_password_here';
   ```

### **Step 2: Run Migration**
1. Visit: `https://yourdomain.com/migrate_smart_contract.php?password=your_secure_password_here`
2. Wait for completion message
3. **IMPORTANT**: Delete the migration file after running!

---

## **Option 2: Manual Database Setup**

### **Step 1: Access phpMyAdmin**
1. Login to cPanel
2. Find "phpMyAdmin" in Databases section
3. Select your TokenLite database

### **Step 2: Run SQL Commands**
Copy and paste the contents of `CPANEL_DATABASE_SETUP.sql` into phpMyAdmin SQL tab and execute.

---

## **Option 3: cPanel Terminal (If Available)**

### **Step 1: Check for Terminal**
1. Login to cPanel
2. Look for "Terminal" in Advanced section
3. If available, navigate to your app folder:
   ```bash
   cd public_html/tokenlite_app
   php artisan migrate
   ```

---

## **🔧 CPANEL FILE STRUCTURE**

### **Recommended Upload Structure:**
```
public_html/
├── tokenlite_app/          # Main application folder
│   ├── app/
│   ├── config/
│   ├── database/
│   ├── resources/
│   ├── .env               # Your environment file
│   └── ...
├── index.php              # Point to tokenlite_app/public/index.php
├── .htaccess              # Redirect rules
└── migrate_smart_contract.php  # Temporary migration script
```

### **Public Folder Setup:**
If your host requires files in `public_html`, copy contents of `tokenlite_app/public/` to `public_html/` and update paths in `index.php`.

---

## **🔒 SECURITY CONSIDERATIONS**

### **Environment File (.env)**
Make sure your `.env` file is properly configured:
```env
# Database Configuration
DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_database_name
DB_USERNAME=your_db_username
DB_PASSWORD=your_db_password

# Smart Contract Configuration - BSC MAINNET
CONTRACT_ADDRESS=0x0df5ba78886ab07b03021db5d4b5ca7275cfe934
CONTRACT_IMPLEMENTATION_ADDRESS=0x779042c47025872616084c237062cccf99a29947
CONTRACT_NETWORK=bsc
CONTRACT_NETWORK_ID=56
WEB3_RPC_URL=https://bsc-dataseed.binance.org/
CONTRACT_ENABLED=true
CONTRACT_DECIMALS=18
CONTRACT_GAS_LIMIT=100000
CONTRACT_GAS_PRICE=5000000000
CONTRACT_IS_PROXY=true
CONTRACT_PROXY_TYPE=EIP1967
```

### **File Permissions:**
Set proper permissions in cPanel File Manager:
- Folders: 755
- Files: 644
- Storage folder: 777 (if needed)

---

## **✅ VERIFICATION STEPS**

### **Step 1: Check Database**
In phpMyAdmin, verify:
1. `transactions` table has new smart contract columns
2. `smart_contract_logs` table exists
3. `settings` table has smart contract entries

### **Step 2: Test Admin Panel**
1. Login to admin panel
2. Go to ICO/STO Settings
3. Scroll to "Smart Contract Settings"
4. Verify BSC Mainnet is selected
5. Click "Test Connection"

### **Step 3: Check User Interface**
1. Visit user dashboard
2. Verify smart contract info is displayed
3. Check token purchase page shows BSC details

---

## **🚨 TROUBLESHOOTING**

### **Common cPanel Issues:**

#### **1. Database Connection Error**
- Check database credentials in `.env`
- Verify database exists in cPanel
- Check database user permissions

#### **2. File Permission Errors**
- Set storage folder to 777
- Check .env file is readable
- Verify file ownership

#### **3. Migration Script Errors**
- Check PHP version (requires 7.4+)
- Verify Laravel dependencies are uploaded
- Check error logs in cPanel

#### **4. Smart Contract Test Fails**
- Verify RPC URL is accessible
- Check contract addresses are correct
- Ensure BSC network is selected

---

## **📱 CPANEL HOSTING RECOMMENDATIONS**

### **Minimum Requirements:**
- PHP 7.4 or higher
- MySQL 5.7 or higher
- 512MB RAM minimum
- SSL certificate for HTTPS

### **Recommended Hosts for TokenLite:**
- SiteGround
- A2 Hosting
- InMotion Hosting
- Hostinger
- Namecheap

---

## **🎯 POST-DEPLOYMENT CHECKLIST**

### **Immediate Tasks:**
- [ ] Database migration completed
- [ ] Admin panel accessible
- [ ] Smart contract settings configured
- [ ] BSC network selected
- [ ] Test connection successful
- [ ] Migration script deleted (security)

### **Configuration Tasks:**
- [ ] SSL certificate installed
- [ ] Email settings configured
- [ ] Payment gateways setup
- [ ] Smart contract enabled
- [ ] Token distribution settings

### **Testing Tasks:**
- [ ] User registration works
- [ ] Token purchase flow
- [ ] Smart contract info displays
- [ ] Admin controls functional
- [ ] Email notifications

---

## **🚀 GO LIVE PROCESS**

### **Final Steps:**
1. ✅ Complete database setup (Option 1, 2, or 3)
2. ✅ Verify admin panel works
3. ✅ Test smart contract connection
4. ✅ Enable smart contract features
5. ✅ Configure token distribution settings
6. ✅ Test complete user flow
7. ✅ Remove migration files
8. ✅ **GO LIVE!**

---

## **📞 SUPPORT**

### **If You Need Help:**
1. Check cPanel error logs
2. Verify database structure in phpMyAdmin
3. Test each component individually
4. Check Laravel logs in storage/logs/

### **Common Solutions:**
- Clear browser cache
- Check file permissions
- Verify database credentials
- Restart PHP processes (if available)

---

## **🎊 SUCCESS!**

Once completed, your TokenLite application will have:
- ✅ Complete BSC Mainnet integration
- ✅ Proxy contract support
- ✅ Enhanced admin controls
- ✅ User-friendly interface
- ✅ Production-ready deployment

**Your smart contract integration is now LIVE on cPanel!** 🚀
