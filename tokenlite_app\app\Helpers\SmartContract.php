<?php

namespace App\Helpers;

use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

/**
 * Smart Contract Helper Class
 * 
 * Handles Web3 interactions, token distribution, and contract communication
 * 
 * @package TokenLite
 * <AUTHOR> Integration
 * @version 1.0.0
 */
class SmartContract
{
    /**
     * Contract addresses
     */
    private $contractAddress;
    private $implementationAddress;

    /**
     * Network configuration
     */
    private $network;
    private $networkId;
    private $rpcUrl;

    /**
     * Contract settings
     */
    private $enabled;
    private $decimals;
    private $gasLimit;
    private $gasPrice;

    /**
     * Proxy contract settings
     */
    private $isProxy;
    private $proxyType;

    /**
     * ERC-20 Token ABI (Essential functions only)
     */
    private $tokenABI = [
        [
            "constant" => true,
            "inputs" => [],
            "name" => "name",
            "outputs" => [["name" => "", "type" => "string"]],
            "type" => "function"
        ],
        [
            "constant" => true,
            "inputs" => [],
            "name" => "symbol",
            "outputs" => [["name" => "", "type" => "string"]],
            "type" => "function"
        ],
        [
            "constant" => true,
            "inputs" => [],
            "name" => "decimals",
            "outputs" => [["name" => "", "type" => "uint8"]],
            "type" => "function"
        ],
        [
            "constant" => true,
            "inputs" => [],
            "name" => "totalSupply",
            "outputs" => [["name" => "", "type" => "uint256"]],
            "type" => "function"
        ],
        [
            "constant" => true,
            "inputs" => [["name" => "_owner", "type" => "address"]],
            "name" => "balanceOf",
            "outputs" => [["name" => "balance", "type" => "uint256"]],
            "type" => "function"
        ],
        [
            "constant" => false,
            "inputs" => [
                ["name" => "_to", "type" => "address"],
                ["name" => "_value", "type" => "uint256"]
            ],
            "name" => "transfer",
            "outputs" => [["name" => "", "type" => "bool"]],
            "type" => "function"
        ]
    ];

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->contractAddress = env('CONTRACT_ADDRESS');
        $this->implementationAddress = env('CONTRACT_IMPLEMENTATION_ADDRESS', '0x779042c47025872616084c237062cccf99a29947');
        $this->network = env('CONTRACT_NETWORK', 'bsc');
        $this->networkId = env('CONTRACT_NETWORK_ID', 56);
        $this->rpcUrl = env('WEB3_RPC_URL');
        $this->enabled = env('CONTRACT_ENABLED', false);
        $this->decimals = env('CONTRACT_DECIMALS', 18);
        $this->gasLimit = env('CONTRACT_GAS_LIMIT', 100000);
        $this->gasPrice = env('CONTRACT_GAS_PRICE', 20000000000);
        $this->isProxy = env('CONTRACT_IS_PROXY', true);
        $this->proxyType = env('CONTRACT_PROXY_TYPE', 'EIP1967');
    }

    /**
     * Check if smart contract integration is enabled
     */
    public function isEnabled()
    {
        return $this->enabled && !empty($this->contractAddress) && !empty($this->rpcUrl);
    }

    /**
     * Get contract address (proxy address)
     */
    public function getContractAddress()
    {
        return $this->contractAddress;
    }

    /**
     * Get implementation address
     */
    public function getImplementationAddress()
    {
        return $this->implementationAddress;
    }

    /**
     * Check if contract is a proxy
     */
    public function isProxy()
    {
        return $this->isProxy;
    }

    /**
     * Get proxy type
     */
    public function getProxyType()
    {
        return $this->proxyType;
    }

    /**
     * Get contract network
     */
    public function getNetwork()
    {
        return $this->network;
    }

    /**
     * Get contract decimals
     */
    public function getDecimals()
    {
        return $this->decimals;
    }

    /**
     * Make RPC call to blockchain
     */
    private function makeRpcCall($method, $params = [])
    {
        try {
            $response = Http::timeout(30)->post($this->rpcUrl, [
                'jsonrpc' => '2.0',
                'method' => $method,
                'params' => $params,
                'id' => 1
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['error'])) {
                    throw new Exception('RPC Error: ' . $data['error']['message']);
                }
                return $data['result'] ?? null;
            }

            throw new Exception('HTTP Error: ' . $response->status());
        } catch (Exception $e) {
            Log::error('Smart Contract RPC Call Failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Call contract method (read-only)
     */
    public function callContract($method, $params = [])
    {
        if (!$this->isEnabled()) {
            throw new Exception('Smart contract integration is not enabled');
        }

        try {
            // Encode function call
            $functionSignature = $this->encodeFunctionCall($method, $params);
            
            $result = $this->makeRpcCall('eth_call', [
                [
                    'to' => $this->contractAddress,
                    'data' => $functionSignature
                ],
                'latest'
            ]);

            return $this->decodeResult($result, $method);
        } catch (Exception $e) {
            Log::error('Contract call failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get token name from contract
     */
    public function getTokenName()
    {
        try {
            return $this->callContract('name');
        } catch (Exception $e) {
            Log::warning('Failed to get token name from contract: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get token symbol from contract
     */
    public function getTokenSymbol()
    {
        try {
            return $this->callContract('symbol');
        } catch (Exception $e) {
            Log::warning('Failed to get token symbol from contract: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get token total supply
     */
    public function getTotalSupply()
    {
        try {
            $supply = $this->callContract('totalSupply');
            return $this->fromWei($supply);
        } catch (Exception $e) {
            Log::warning('Failed to get total supply from contract: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get balance of an address
     */
    public function getBalance($address)
    {
        try {
            $balance = $this->callContract('balanceOf', [$address]);
            return $this->fromWei($balance);
        } catch (Exception $e) {
            Log::warning('Failed to get balance from contract: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Convert Wei to token units
     */
    public function fromWei($wei)
    {
        if (empty($wei) || $wei === '0x0') {
            return 0;
        }
        
        $wei = is_string($wei) && strpos($wei, '0x') === 0 ? hexdec($wei) : $wei;
        return $wei / pow(10, $this->decimals);
    }

    /**
     * Convert token units to Wei
     */
    public function toWei($amount)
    {
        return bcmul($amount, bcpow(10, $this->decimals));
    }

    /**
     * Encode function call for contract interaction
     */
    private function encodeFunctionCall($method, $params = [])
    {
        // Simple function signature encoding for basic ERC-20 methods
        $signatures = [
            'name' => '0x06fdde03',
            'symbol' => '0x95d89b41',
            'decimals' => '0x313ce567',
            'totalSupply' => '0x18160ddd',
            'balanceOf' => '0x70a08231'
        ];

        if (!isset($signatures[$method])) {
            throw new Exception('Unsupported method: ' . $method);
        }

        $signature = $signatures[$method];

        // For balanceOf, we need to encode the address parameter
        if ($method === 'balanceOf' && !empty($params[0])) {
            $address = str_replace('0x', '', $params[0]);
            $signature .= str_pad($address, 64, '0', STR_PAD_LEFT);
        }

        return $signature;
    }

    /**
     * Decode contract call result
     */
    private function decodeResult($result, $method)
    {
        if (empty($result) || $result === '0x') {
            return null;
        }

        switch ($method) {
            case 'name':
            case 'symbol':
                return $this->decodeString($result);
            case 'decimals':
                return hexdec($result);
            case 'totalSupply':
            case 'balanceOf':
                return $result;
            default:
                return $result;
        }
    }

    /**
     * Decode string from hex
     */
    private function decodeString($hex)
    {
        $hex = str_replace('0x', '', $hex);
        $length = hexdec(substr($hex, 64, 64));
        $string = substr($hex, 128, $length * 2);
        return hex2bin($string);
    }

    /**
     * Validate Ethereum address
     */
    public function isValidAddress($address)
    {
        return preg_match('/^0x[a-fA-F0-9]{40}$/', $address);
    }

    /**
     * Get contract information
     */
    public function getContractInfo()
    {
        if (!$this->isEnabled()) {
            return [
                'enabled' => false,
                'message' => 'Smart contract integration is not enabled'
            ];
        }

        try {
            return [
                'enabled' => true,
                'address' => $this->contractAddress,
                'network' => $this->network,
                'name' => $this->getTokenName(),
                'symbol' => $this->getTokenSymbol(),
                'decimals' => $this->decimals,
                'totalSupply' => $this->getTotalSupply()
            ];
        } catch (Exception $e) {
            return [
                'enabled' => true,
                'address' => $this->contractAddress,
                'network' => $this->network,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get implementation address from proxy contract
     */
    public function getImplementationFromProxy()
    {
        if (!$this->isProxy()) {
            return $this->contractAddress;
        }

        try {
            // EIP-1967 implementation slot: keccak256("eip1967.proxy.implementation") - 1
            $implementationSlot = '0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc';

            $result = $this->makeRpcCall('eth_getStorageAt', [
                $this->contractAddress,
                $implementationSlot,
                'latest'
            ]);

            if ($result && $result !== '0x0000000000000000000000000000000000000000000000000000000000000000') {
                // Extract address from storage slot (last 20 bytes)
                $implementationAddress = '0x' . substr($result, -40);
                return $implementationAddress;
            }

            // Fallback to configured implementation address
            return $this->implementationAddress;

        } catch (Exception $e) {
            Log::warning('Failed to get implementation address from proxy: ' . $e->getMessage());
            return $this->implementationAddress;
        }
    }

    /**
     * Get proxy admin address
     */
    public function getProxyAdmin()
    {
        if (!$this->isProxy()) {
            return null;
        }

        try {
            // EIP-1967 admin slot: keccak256("eip1967.proxy.admin") - 1
            $adminSlot = '0xb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103';

            $result = $this->makeRpcCall('eth_getStorageAt', [
                $this->contractAddress,
                $adminSlot,
                'latest'
            ]);

            if ($result && $result !== '0x0000000000000000000000000000000000000000000000000000000000000000') {
                return '0x' . substr($result, -40);
            }

            return null;

        } catch (Exception $e) {
            Log::warning('Failed to get proxy admin address: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Get comprehensive proxy information
     */
    public function getProxyInfo()
    {
        if (!$this->isProxy()) {
            return [
                'is_proxy' => false,
                'proxy_address' => $this->contractAddress
            ];
        }

        return [
            'is_proxy' => true,
            'proxy_type' => $this->proxyType,
            'proxy_address' => $this->contractAddress,
            'implementation_address' => $this->getImplementationFromProxy(),
            'configured_implementation' => $this->implementationAddress,
            'admin_address' => $this->getProxyAdmin()
        ];
    }

    /**
     * Log contract interaction
     */
    public function logInteraction($action, $data = [])
    {
        $logData = [
            'action' => $action,
            'contract' => $this->contractAddress,
            'network' => $this->network,
            'data' => $data
        ];

        if ($this->isProxy()) {
            $logData['proxy_info'] = [
                'is_proxy' => true,
                'implementation' => $this->implementationAddress,
                'proxy_type' => $this->proxyType
            ];
        }

        Log::info('Smart Contract Interaction', $logData);
    }
}
