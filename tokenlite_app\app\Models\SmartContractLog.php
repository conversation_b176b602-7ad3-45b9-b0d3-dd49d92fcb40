<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

/**
 * Smart Contract Log Model
 * 
 * Manages smart contract interaction logs
 * 
 * @package TokenLite
 * <AUTHOR> Integration
 * @version 1.0.0
 */
class SmartContractLog extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'smart_contract_logs';

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'transaction_id',
        'user_id',
        'contract_address',
        'network',
        'action',
        'method',
        'parameters',
        'response',
        'blockchain_hash',
        'status',
        'error_message',
        'gas_used',
        'gas_price',
        'sent_at',
        'confirmed_at'
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'parameters' => 'array',
        'response' => 'array',
        'sent_at' => 'datetime',
        'confirmed_at' => 'datetime'
    ];

    /**
     * Get the transaction that owns the log.
     */
    public function transaction()
    {
        return $this->belongsTo(Transaction::class);
    }

    /**
     * Get the user that owns the log.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Create a new smart contract log entry
     */
    public static function createLog($data)
    {
        return self::create([
            'transaction_id' => $data['transaction_id'] ?? null,
            'user_id' => $data['user_id'] ?? null,
            'contract_address' => $data['contract_address'],
            'network' => $data['network'],
            'action' => $data['action'],
            'method' => $data['method'] ?? null,
            'parameters' => $data['parameters'] ?? null,
            'response' => $data['response'] ?? null,
            'blockchain_hash' => $data['blockchain_hash'] ?? null,
            'status' => $data['status'] ?? 'pending',
            'error_message' => $data['error_message'] ?? null,
            'gas_used' => $data['gas_used'] ?? null,
            'gas_price' => $data['gas_price'] ?? null,
            'sent_at' => $data['sent_at'] ?? null,
            'confirmed_at' => $data['confirmed_at'] ?? null
        ]);
    }

    /**
     * Get logs by transaction
     */
    public static function getByTransaction($transactionId)
    {
        return self::where('transaction_id', $transactionId)
                   ->orderBy('created_at', 'desc')
                   ->get();
    }

    /**
     * Get logs by user
     */
    public static function getByUser($userId)
    {
        return self::where('user_id', $userId)
                   ->orderBy('created_at', 'desc')
                   ->get();
    }

    /**
     * Get logs by contract address
     */
    public static function getByContract($contractAddress)
    {
        return self::where('contract_address', $contractAddress)
                   ->orderBy('created_at', 'desc')
                   ->get();
    }

    /**
     * Get logs by status
     */
    public static function getByStatus($status)
    {
        return self::where('status', $status)
                   ->orderBy('created_at', 'desc')
                   ->get();
    }

    /**
     * Update log status
     */
    public function updateStatus($status, $data = [])
    {
        $updateData = ['status' => $status];
        
        if (isset($data['blockchain_hash'])) {
            $updateData['blockchain_hash'] = $data['blockchain_hash'];
        }
        
        if (isset($data['response'])) {
            $updateData['response'] = $data['response'];
        }
        
        if (isset($data['error_message'])) {
            $updateData['error_message'] = $data['error_message'];
        }
        
        if (isset($data['gas_used'])) {
            $updateData['gas_used'] = $data['gas_used'];
        }
        
        if ($status === 'success' && !$this->confirmed_at) {
            $updateData['confirmed_at'] = now();
        }
        
        return $this->update($updateData);
    }

    /**
     * Check if log is successful
     */
    public function isSuccessful()
    {
        return $this->status === 'success';
    }

    /**
     * Check if log is failed
     */
    public function isFailed()
    {
        return $this->status === 'failed';
    }

    /**
     * Check if log is pending
     */
    public function isPending()
    {
        return $this->status === 'pending';
    }

    /**
     * Get formatted parameters
     */
    public function getFormattedParametersAttribute()
    {
        if (is_array($this->parameters)) {
            return json_encode($this->parameters, JSON_PRETTY_PRINT);
        }
        return $this->parameters;
    }

    /**
     * Get formatted response
     */
    public function getFormattedResponseAttribute()
    {
        if (is_array($this->response)) {
            return json_encode($this->response, JSON_PRETTY_PRINT);
        }
        return $this->response;
    }
}
