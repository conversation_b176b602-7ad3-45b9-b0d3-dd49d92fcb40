<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Smart Contract Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains the configuration for smart contract interactions
    | including ABIs, network configurations, and contract addresses.
    |
    */

    'default_network' => env('CONTRACT_NETWORK', 'bsc'),

    'networks' => [
        'ethereum' => [
            'name' => 'Ethereum Mainnet',
            'chain_id' => 1,
            'rpc_url' => env('WEB3_RPC_URL', 'https://mainnet.infura.io/v3/'),
            'explorer' => 'https://etherscan.io',
            'currency' => 'ETH',
        ],
        'goerli' => [
            'name' => 'Goerli Testnet',
            'chain_id' => 5,
            'rpc_url' => env('WEB3_RPC_URL', 'https://goerli.infura.io/v3/'),
            'explorer' => 'https://goerli.etherscan.io',
            'currency' => 'ETH',
        ],
        'sepolia' => [
            'name' => 'Sepolia Testnet',
            'chain_id' => 11155111,
            'rpc_url' => env('WEB3_RPC_URL', 'https://sepolia.infura.io/v3/'),
            'explorer' => 'https://sepolia.etherscan.io',
            'currency' => 'ETH',
        ],
        'bsc' => [
            'name' => 'BSC Mainnet',
            'chain_id' => 56,
            'rpc_url' => env('WEB3_RPC_URL', 'https://bsc-dataseed.binance.org/'),
            'explorer' => 'https://bscscan.com',
            'currency' => 'BNB',
        ],
        'polygon' => [
            'name' => 'Polygon Mainnet',
            'chain_id' => 137,
            'rpc_url' => env('WEB3_RPC_URL', 'https://polygon-rpc.com/'),
            'explorer' => 'https://polygonscan.com',
            'currency' => 'MATIC',
        ],
    ],

    'contracts' => [
        'token' => [
            'address' => env('CONTRACT_ADDRESS', '******************************************'),
            'implementation_address' => env('CONTRACT_IMPLEMENTATION_ADDRESS', '******************************************'),
            'network' => env('CONTRACT_NETWORK', 'bsc'),
            'type' => 'BEP20',
            'decimals' => env('CONTRACT_DECIMALS', 18),
            'is_proxy' => env('CONTRACT_IS_PROXY', true),
            'proxy_type' => env('CONTRACT_PROXY_TYPE', 'EIP1967'),
        ],
    ],

    'proxy' => [
        'eip1967' => [
            'implementation_slot' => '0x360894a13ba1a3210667c828492db98dca3e2076cc3735a920a3ca505d382bbc',
            'admin_slot' => '0xb53127684a568b3173ae13b9f8a6016e243e63b6e8ee1178d6a717850b5d6103',
            'beacon_slot' => '0xa3f0ad74e5423aebfd80d3ef4346578335a9a72aeaee59ff6cb3582b35133d50',
        ],
        'supported_types' => ['EIP1967', 'OpenZeppelin', 'Custom'],
    ],

    'abis' => [
        'Proxy' => [
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'implementation',
                'outputs' => [['name' => '', 'type' => 'address']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'admin',
                'outputs' => [['name' => '', 'type' => 'address']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [['name' => 'newImplementation', 'type' => 'address']],
                'name' => 'upgradeTo',
                'outputs' => [],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => 'newImplementation', 'type' => 'address'],
                    ['name' => 'data', 'type' => 'bytes']
                ],
                'name' => 'upgradeToAndCall',
                'outputs' => [],
                'type' => 'function'
            ]
        ],
        'ERC20' => [
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'name',
                'outputs' => [['name' => '', 'type' => 'string']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'symbol',
                'outputs' => [['name' => '', 'type' => 'string']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'decimals',
                'outputs' => [['name' => '', 'type' => 'uint8']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'totalSupply',
                'outputs' => [['name' => '', 'type' => 'uint256']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [['name' => '_owner', 'type' => 'address']],
                'name' => 'balanceOf',
                'outputs' => [['name' => 'balance', 'type' => 'uint256']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => '_to', 'type' => 'address'],
                    ['name' => '_value', 'type' => 'uint256']
                ],
                'name' => 'transfer',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [
                    ['name' => '_owner', 'type' => 'address'],
                    ['name' => '_spender', 'type' => 'address']
                ],
                'name' => 'allowance',
                'outputs' => [['name' => '', 'type' => 'uint256']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => '_spender', 'type' => 'address'],
                    ['name' => '_value', 'type' => 'uint256']
                ],
                'name' => 'approve',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => '_from', 'type' => 'address'],
                    ['name' => '_to', 'type' => 'address'],
                    ['name' => '_value', 'type' => 'uint256']
                ],
                'name' => 'transferFrom',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'anonymous' => false,
                'inputs' => [
                    ['indexed' => true, 'name' => 'from', 'type' => 'address'],
                    ['indexed' => true, 'name' => 'to', 'type' => 'address'],
                    ['indexed' => false, 'name' => 'value', 'type' => 'uint256']
                ],
                'name' => 'Transfer',
                'type' => 'event'
            ],
            [
                'anonymous' => false,
                'inputs' => [
                    ['indexed' => true, 'name' => 'owner', 'type' => 'address'],
                    ['indexed' => true, 'name' => 'spender', 'type' => 'address'],
                    ['indexed' => false, 'name' => 'value', 'type' => 'uint256']
                ],
                'name' => 'Approval',
                'type' => 'event'
            ]
        ],
        'ExtendedERC20' => [
            // All ERC20 functions plus additional common functions
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'name',
                'outputs' => [['name' => '', 'type' => 'string']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'symbol',
                'outputs' => [['name' => '', 'type' => 'string']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'decimals',
                'outputs' => [['name' => '', 'type' => 'uint8']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'totalSupply',
                'outputs' => [['name' => '', 'type' => 'uint256']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [['name' => '_owner', 'type' => 'address']],
                'name' => 'balanceOf',
                'outputs' => [['name' => 'balance', 'type' => 'uint256']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => '_to', 'type' => 'address'],
                    ['name' => '_value', 'type' => 'uint256']
                ],
                'name' => 'transfer',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [
                    ['name' => '_owner', 'type' => 'address'],
                    ['name' => '_spender', 'type' => 'address']
                ],
                'name' => 'allowance',
                'outputs' => [['name' => '', 'type' => 'uint256']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => '_spender', 'type' => 'address'],
                    ['name' => '_value', 'type' => 'uint256']
                ],
                'name' => 'approve',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => '_from', 'type' => 'address'],
                    ['name' => '_to', 'type' => 'address'],
                    ['name' => '_value', 'type' => 'uint256']
                ],
                'name' => 'transferFrom',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            // Additional common functions
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'owner',
                'outputs' => [['name' => '', 'type' => 'address']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => '_to', 'type' => 'address'],
                    ['name' => '_amount', 'type' => 'uint256']
                ],
                'name' => 'mint',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [['name' => '_amount', 'type' => 'uint256']],
                'name' => 'burn',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => '_from', 'type' => 'address'],
                    ['name' => '_amount', 'type' => 'uint256']
                ],
                'name' => 'burnFrom',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [],
                'name' => 'pause',
                'outputs' => [],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [],
                'name' => 'unpause',
                'outputs' => [],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [],
                'name' => 'paused',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [
                    ['name' => '_recipients', 'type' => 'address[]'],
                    ['name' => '_amounts', 'type' => 'uint256[]']
                ],
                'name' => 'batchTransfer',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => true,
                'inputs' => [['name' => '_account', 'type' => 'address']],
                'name' => 'isBlacklisted',
                'outputs' => [['name' => '', 'type' => 'bool']],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [['name' => '_account', 'type' => 'address']],
                'name' => 'blacklist',
                'outputs' => [],
                'type' => 'function'
            ],
            [
                'constant' => false,
                'inputs' => [['name' => '_account', 'type' => 'address']],
                'name' => 'unBlacklist',
                'outputs' => [],
                'type' => 'function'
            ],
            // Events
            [
                'anonymous' => false,
                'inputs' => [
                    ['indexed' => true, 'name' => 'from', 'type' => 'address'],
                    ['indexed' => true, 'name' => 'to', 'type' => 'address'],
                    ['indexed' => false, 'name' => 'value', 'type' => 'uint256']
                ],
                'name' => 'Transfer',
                'type' => 'event'
            ],
            [
                'anonymous' => false,
                'inputs' => [
                    ['indexed' => true, 'name' => 'owner', 'type' => 'address'],
                    ['indexed' => true, 'name' => 'spender', 'type' => 'address'],
                    ['indexed' => false, 'name' => 'value', 'type' => 'uint256']
                ],
                'name' => 'Approval',
                'type' => 'event'
            ],
            [
                'anonymous' => false,
                'inputs' => [
                    ['indexed' => true, 'name' => 'to', 'type' => 'address'],
                    ['indexed' => false, 'name' => 'amount', 'type' => 'uint256']
                ],
                'name' => 'Mint',
                'type' => 'event'
            ],
            [
                'anonymous' => false,
                'inputs' => [
                    ['indexed' => true, 'name' => 'from', 'type' => 'address'],
                    ['indexed' => false, 'name' => 'amount', 'type' => 'uint256']
                ],
                'name' => 'Burn',
                'type' => 'event'
            ],
            [
                'anonymous' => false,
                'inputs' => [
                    ['indexed' => true, 'name' => 'account', 'type' => 'address']
                ],
                'name' => 'Paused',
                'type' => 'event'
            ],
            [
                'anonymous' => false,
                'inputs' => [
                    ['indexed' => true, 'name' => 'account', 'type' => 'address']
                ],
                'name' => 'Unpaused',
                'type' => 'event'
            ]
        ],
    ],

    'gas' => [
        'default_limit' => env('CONTRACT_GAS_LIMIT', 100000),
        'default_price' => env('CONTRACT_GAS_PRICE', ***********), // 20 Gwei
        'transfer_limit' => 21000,
        'contract_call_limit' => 100000,
        'contract_deploy_limit' => 2000000,
    ],

    'timeouts' => [
        'rpc_timeout' => env('WEB3_TIMEOUT', 30),
        'confirmation_timeout' => env('CONTRACT_CONFIRMATION_TIMEOUT', 300), // 5 minutes
        'retry_attempts' => env('WEB3_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('WEB3_RETRY_DELAY', 1000), // milliseconds
    ],

    'features' => [
        'auto_distribution' => env('CONTRACT_AUTO_DISTRIBUTE', false),
        'confirmation_required' => env('CONTRACT_CONFIRMATION_REQUIRED', true),
        'min_confirmations' => env('CONTRACT_MIN_CONFIRMATIONS', 12),
        'enable_logs' => env('CONTRACT_ENABLE_LOGS', true),
    ],

];
