<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Stripe, Mailgun, SparkPost and others. This file provides a sane
    | default location for this type of information, allowing packages
    | to have a conventional place to find your various credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'ses' => [
        'key' => env('SES_KEY'),
        'secret' => env('SES_SECRET'),
        'region' => env('SES_REGION', 'us-east-1'),
    ],

    'sendgrid' => [
        'api_key' => env('SENDGRID_API_KEY'),
    ],
    
    'sparkpost' => [
        'secret' => env('SPARKPOST_SECRET'),
    ],

    'stripe' => [
        'model' => App\Models\User::class,
        'key' => env('STRIPE_KEY'),
        'secret' => env('STRIPE_SECRET'),
        'webhook' => [
            'secret' => env('STRIPE_WEBHOOK_SECRET'),
            'tolerance' => env('STRIPE_WEBHOOK_TOLERANCE', 300),
        ],
    ],


    'facebook' => [
        'client_id' => env('FB_CLIENT_ID'),
        'client_secret' => env('FB_CLIENT_SECRET'),
        'redirect' => env('APP_URL').'/auth/facebook/callback',
    ],

    'google' => [
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('APP_URL').'/auth/google/callback',
    ],

    'blockchain' => [
        'contract_address' => env('CONTRACT_ADDRESS'),
        'network' => env('CONTRACT_NETWORK', 'ethereum'),
        'network_id' => env('CONTRACT_NETWORK_ID', 1),
        'rpc_url' => env('WEB3_RPC_URL'),
        'enabled' => env('CONTRACT_ENABLED', false),
        'decimals' => env('CONTRACT_DECIMALS', 18),
        'gas_limit' => env('CONTRACT_GAS_LIMIT', 100000),
        'gas_price' => env('CONTRACT_GAS_PRICE', 20000000000),
    ],

    'web3' => [
        'rpc_url' => env('WEB3_RPC_URL'),
        'timeout' => env('WEB3_TIMEOUT', 30),
        'retry_attempts' => env('WEB3_RETRY_ATTEMPTS', 3),
        'retry_delay' => env('WEB3_RETRY_DELAY', 1000),
    ],

];
