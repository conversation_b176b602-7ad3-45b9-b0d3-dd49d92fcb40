<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddSmartContractFieldsToTransactions extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('transactions', function (Blueprint $table) {
            // Smart contract related fields
            $table->string('contract_address')->nullable()->after('details');
            $table->string('contract_network')->nullable()->after('contract_address');
            $table->string('blockchain_hash')->nullable()->after('contract_network');
            $table->string('blockchain_status')->default('pending')->after('blockchain_hash');
            $table->text('blockchain_data')->nullable()->after('blockchain_status');
            $table->timestamp('blockchain_sent_at')->nullable()->after('blockchain_data');
            $table->timestamp('blockchain_confirmed_at')->nullable()->after('blockchain_sent_at');
            $table->integer('blockchain_confirmations')->default(0)->after('blockchain_confirmed_at');
            $table->string('token_distribution_status')->default('pending')->after('blockchain_confirmations');
            $table->text('token_distribution_data')->nullable()->after('token_distribution_status');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('transactions', function (Blueprint $table) {
            $table->dropColumn([
                'contract_address',
                'contract_network', 
                'blockchain_hash',
                'blockchain_status',
                'blockchain_data',
                'blockchain_sent_at',
                'blockchain_confirmed_at',
                'blockchain_confirmations',
                'token_distribution_status',
                'token_distribution_data'
            ]);
        });
    }
}
