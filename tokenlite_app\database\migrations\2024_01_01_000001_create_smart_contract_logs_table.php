<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateSmartContractLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('smart_contract_logs', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('transaction_id')->nullable();
            $table->integer('user_id')->nullable();
            $table->string('contract_address');
            $table->string('network');
            $table->string('action'); // call, transfer, balance_check, etc.
            $table->string('method')->nullable(); // contract method called
            $table->text('parameters')->nullable(); // JSON parameters
            $table->text('response')->nullable(); // JSON response
            $table->string('blockchain_hash')->nullable();
            $table->string('status'); // pending, success, failed
            $table->text('error_message')->nullable();
            $table->integer('gas_used')->nullable();
            $table->string('gas_price')->nullable();
            $table->timestamp('sent_at')->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamps();
            
            $table->index(['transaction_id', 'user_id']);
            $table->index(['contract_address', 'network']);
            $table->index(['status', 'action']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('smart_contract_logs');
    }
}
