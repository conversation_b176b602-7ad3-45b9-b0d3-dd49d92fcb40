<?php
/**
 * Smart Contract Migration Script for cPanel
 * 
 * Upload this file to your public folder and run via browser
 * URL: yourdomain.com/migrate_smart_contract.php
 * 
 * IMPORTANT: Delete this file after running!
 */

// Security check - change this password
$MIGRATION_PASSWORD = 'your_secure_password_here';

if (!isset($_GET['password']) || $_GET['password'] !== $MIGRATION_PASSWORD) {
    die('Access denied. Add ?password=your_secure_password_here to URL');
}

// Include Laravel bootstrap
require_once __DIR__ . '/../vendor/autoload.php';

try {
    // Bootstrap Laravel
    $app = require_once __DIR__ . '/../bootstrap/app.php';
    $kernel = $app->make(Illuminate\Contracts\Console\Kernel::class);
    $kernel->bootstrap();

    echo "<h1>Smart Contract Migration for cPanel</h1>";
    echo "<pre>";

    // Get database connection
    $db = DB::connection();
    
    echo "✅ Database connection successful\n\n";

    // Check if migrations are needed
    echo "🔍 Checking current database structure...\n";
    
    // Check if smart contract fields exist in transactions table
    $transactionColumns = $db->select("SHOW COLUMNS FROM transactions");
    $hasContractFields = false;
    foreach ($transactionColumns as $column) {
        if ($column->Field === 'contract_address') {
            $hasContractFields = true;
            break;
        }
    }

    // Check if smart_contract_logs table exists
    $tables = $db->select("SHOW TABLES LIKE 'smart_contract_logs'");
    $hasLogsTable = count($tables) > 0;

    if (!$hasContractFields) {
        echo "📝 Adding smart contract fields to transactions table...\n";
        
        $db->statement("ALTER TABLE `transactions` 
            ADD COLUMN `contract_address` VARCHAR(255) NULL AFTER `details`,
            ADD COLUMN `contract_network` VARCHAR(255) NULL AFTER `contract_address`,
            ADD COLUMN `blockchain_hash` VARCHAR(255) NULL AFTER `contract_network`,
            ADD COLUMN `blockchain_status` VARCHAR(255) DEFAULT 'pending' AFTER `blockchain_hash`,
            ADD COLUMN `blockchain_data` TEXT NULL AFTER `blockchain_status`,
            ADD COLUMN `blockchain_sent_at` TIMESTAMP NULL AFTER `blockchain_data`,
            ADD COLUMN `blockchain_confirmed_at` TIMESTAMP NULL AFTER `blockchain_sent_at`,
            ADD COLUMN `blockchain_confirmations` INT DEFAULT 0 AFTER `blockchain_confirmed_at`,
            ADD COLUMN `token_distribution_status` VARCHAR(255) DEFAULT 'pending' AFTER `blockchain_confirmations`,
            ADD COLUMN `token_distribution_data` TEXT NULL AFTER `token_distribution_status`");
        
        echo "✅ Smart contract fields added to transactions table\n";
    } else {
        echo "✅ Smart contract fields already exist in transactions table\n";
    }

    if (!$hasLogsTable) {
        echo "📝 Creating smart_contract_logs table...\n";
        
        $db->statement("CREATE TABLE `smart_contract_logs` (
            `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `transaction_id` int(11) NULL,
            `user_id` int(11) NULL,
            `contract_address` varchar(255) NOT NULL,
            `network` varchar(255) NOT NULL,
            `action` varchar(255) NOT NULL,
            `method` varchar(255) NULL,
            `parameters` text NULL,
            `response` text NULL,
            `blockchain_hash` varchar(255) NULL,
            `status` varchar(255) NOT NULL,
            `error_message` text NULL,
            `gas_used` int(11) NULL,
            `gas_price` varchar(255) NULL,
            `sent_at` timestamp NULL,
            `confirmed_at` timestamp NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_transaction_user` (`transaction_id`, `user_id`),
            KEY `idx_contract_network` (`contract_address`, `network`),
            KEY `idx_status_action` (`status`, `action`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci");
        
        echo "✅ Smart contract logs table created\n";
    } else {
        echo "✅ Smart contract logs table already exists\n";
    }

    // Add smart contract settings
    echo "📝 Adding smart contract settings...\n";
    
    $settings = [
        'token_contract_address' => '0x0df5ba78886ab07b03021db5d4b5ca7275cfe934',
        'token_contract_implementation_address' => '0x779042c47025872616084c237062cccf99a29947',
        'token_contract_network' => 'bsc',
        'token_contract_network_id' => '56',
        'token_contract_enabled' => '1',
        'token_contract_decimals' => '18',
        'token_contract_auto_distribute' => '0',
        'token_contract_gas_limit' => '100000',
        'token_contract_gas_price' => '5000000000',
        'web3_rpc_url' => 'https://bsc-dataseed.binance.org/',
        'token_contract_owner_address' => '',
        'token_contract_private_key' => '',
        'token_contract_is_proxy' => '1',
        'token_contract_proxy_type' => 'EIP1967'
    ];

    foreach ($settings as $field => $value) {
        $existing = $db->select("SELECT * FROM settings WHERE field = ?", [$field]);
        
        if (empty($existing)) {
            $db->insert("INSERT INTO settings (field, value, created_at, updated_at) VALUES (?, ?, NOW(), NOW())", 
                [$field, $value]);
            echo "✅ Added setting: {$field}\n";
        } else {
            $db->update("UPDATE settings SET value = ?, updated_at = NOW() WHERE field = ?", 
                [$value, $field]);
            echo "✅ Updated setting: {$field}\n";
        }
    }

    echo "\n🎉 SMART CONTRACT MIGRATION COMPLETED SUCCESSFULLY!\n\n";
    echo "✅ Database structure updated\n";
    echo "✅ Smart contract settings configured\n";
    echo "✅ BSC Mainnet ready\n";
    echo "✅ Proxy contract integration complete\n\n";
    
    echo "🔒 IMPORTANT: Delete this file (migrate_smart_contract.php) for security!\n\n";
    
    echo "🚀 Your TokenLite is now ready with smart contract integration!\n";
    echo "Go to Admin → ICO Settings → Smart Contract Settings to test.\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "</pre>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
pre { background: #f5f5f5; padding: 15px; border-radius: 5px; }
h1 { color: #333; }
</style>
