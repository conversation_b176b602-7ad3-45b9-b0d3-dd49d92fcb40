@extends('layouts.admin')
@section('title', 'ICO/STO Setting')
@php
$wallet_opt = field_value_text('token_wallet_opt' , 'wallet_opt');
is_array($wallet_opt) ? true : $wallet_opt = array();
$custom = field_value_text('token_wallet_custom');
is_array($custom) ? true : $custom = array();
@endphp

@section('content')
<div class="page-content">
    <div class="container">
        <div class="row">
            <div class="main-content col-lg-12">
                @include('vendor.notice')
                <div class="content-area card">
                    <div class="card-innr">
                        <div class="card-head">
                            <h4 class="card-title">ICO/STO Settings </h4>
                        </div>
                        <div class="gaps-1x"></div>
                        <div class="card-text ico-setting setting-token-details">
                            <h3 class="card-title-md text-primary">ICO/STO Token Details</h3>
                            <form action="{{ route('admin.ajax.stages.settings.update') }}" method="POST" id="stage_setting_details_form" class="validate-modern">
                                @csrf
                                <input type="hidden" name="req_type" value="token_details">
                                <div class="row">
                                    <div class="col-xl-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Token Name</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" required type="text" name="token_name" value="{{ token('name') }}" minlength="3">
                                            </div>
                                            <span class="input-note">Enter name of token without spaces. Lower and uppercase can be used.</span>
                                        </div>
                                    </div>
                                    <div class="col-xl-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Token Symbol</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" required type="text" name="token_symbol" value="{{ token('symbol') }}" minlength="2">
                                            </div>
                                            <span class="input-note">Usually 3-4 Letters like ETH, BTC, WISH etc.</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xl-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Decimal Minimum</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="number" name="token_decimal_min" value="{{ token('decimal_min') }}" min="2" max="8">
                                            </div>
                                            <span class="input-note">Minimum number of decimal point for calculation. 2-8 are accepted.</span>
                                        </div>
                                    </div>
                                    <div class="col-xl-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Decimal Maximum</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="number" name="token_decimal_max" value="{{ token('decimal_max') }}" min="6" max="18">
                                            </div>
                                            <span class="input-note">Maximum number of decimal point for calculation. 6-18 are accepted.</span>
                                        </div>
                                    </div>
                                    <div class="col-xl-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Decimal Display</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="number" name="token_decimal_show" value="{{ token('decimal_show') ? token('decimal_show') : 0 }}" min="0" max="8">
                                            </div>
                                            <span class="input-note">The number of decimal point apply to show number in User/Admin Card balance.</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="gaps-1x"></div>
                                <div class="d-flex">
                                    <button class="btn btn-primary save-disabled" type="submit" disabled><i class="ti ti-reload"></i><span>Update</span></button>
                                </div>
                            </form>
                        </div>
                        <div class="sap sap-gap"></div>
                        <div class="card-text ico-setting setting-token-purchase">
                            <h4 class="card-title-md text-primary">Purchase & Addtional Setting</h4>
                            <form action="{{ route('admin.ajax.stages.settings.update') }}" method="POST" id="stage_setting_purchase_form" class="validate-modern">
                                @csrf
                                <input type="hidden" name="req_type" value="token_purchase">
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Default Selection</label>
                                            <div class="input-wrap">
                                                <select class="select select-block select-bordered active_method" name="token_default_method">
                                                    @foreach($pm_gateways as $pmg => $pmval)
                                                    @if(get_setting('pmc_active_'.$pmg) == 1)
                                                    <option {{ token('default_method') == strtoupper($pmg) ? 'selected ' : '' }}value="{{ strtoupper($pmg) }}">{{ $pmval.(($pmg==base_currency()) ? ' (Based)' : '') }}</option>
                                                    @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Token Price Show in</label>
                                            <div class="input-wrap">
                                                <select class="select select-block select-bordered" name="token_default_in_userpanel">
                                                    @foreach($pm_gateways as $pmg => $pmval)
                                                    @if(get_setting('pmc_active_'.$pmg) == 1 && base_currency() != $pmg)
                                                    <option {{ token('default_in_userpanel') == strtoupper($pmg) ? 'selected ' : '' }}value="{{ strtoupper($pmg) }}"> {{ base_currency(true) }} -> {{ strtoupper($pmg) }}</option>
                                                    @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Per Token Price</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="token_price_show" type="checkbox" {{ token('price_show') == 1 ? 'checked' : '' }} id="per-token-price">
                                                <label for="per-token-price">Show</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Amount in Money Format</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="token_number_format" type="checkbox" {{ token('number_format') == 1 ? 'checked' : '' }} id="format-number">
                                                <label for="format-number">Enabled</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">KYC Before Purchase</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="token_before_kyc" type="checkbox" {{ token('before_kyc') == 1 ? 'checked' : '' }} id="kyc-before-buy">
                                                <label for="kyc-before-buy">Enable</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <label class="input-item-label">Purchase With</label>
                                        <ul class="d-flex flex-wrap checkbox-list checkbox-list-c5">
                                            @foreach($pm_gateways as $pmg => $pmval)
                                            @if(get_setting('pmc_active_'.$pmg) == 1)
                                            <li>
                                                <div class="input-item text-left">
                                                    <div class="input-wrap">
                                                        <input class="input-checkbox input-checkbox-sm all_methods" name="token_purchase_{{ $pmg }}" id="pw-{{ $pmg }}" {{ (token('purchase_'.$pmg) == 1) ? 'checked ' : ' '}} {{token('default_method') == strtoupper($pmg) ? 'disabled ' : ' ' }}  type="checkbox">
                                                        <label for="pw-{{ $pmg }}">{{ $pmval .' ('.strtoupper($pmg).')'}}</label>
                                                    </div>
                                                </div>
                                            </li>
                                            @endif
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                                <div class="gaps-2x"></div>
                                <h5 class="card-title-sm text-secondary">Progress Bar Setting</h5>
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Raised Amount Show in</label>
                                            <div class="input-wrap">
                                                <select class="select select-block select-bordered" name="token_sales_raised">
                                                    <option {{ token('sales_raised') == 'token' ? 'selected ' : '' }}value="token">Token Amount</option>
                                                    @foreach($pm_gateways as $pmg => $pmval)
                                                    @if(get_setting('pmc_active_'.$pmg) == 1)
                                                    <option {{ token('sales_raised') == $pmg ? 'selected ' : '' }}value="{{ $pmg }}">{{ $pmval.(($pmg==base_currency()) ? ' (Based)' : '') }}</option>
                                                    @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Total Amount Show in</label>
                                            <div class="input-wrap">
                                                <select class="select select-block select-bordered" name="token_sales_total">
                                                    <option {{ token('sales_total') == 'token' ? 'selected ' : '' }}value="token">Token Amount</option>
                                                    @foreach($pm_gateways as $pmg => $pmval)
                                                    @if(get_setting('pmc_active_'.$pmg) == 1)
                                                    <option {{ token('sales_total') == $pmg ? 'selected ' : '' }}value="{{ $pmg }}">{{ $pmval.(($pmg==base_currency()) ? ' (Based)' : '') }}</option>
                                                    @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Soft/HardCap Show in</label>
                                            <div class="input-wrap">
                                                <select class="select select-block select-bordered" name="token_sales_cap">
                                                    <option {{ token('sales_cap') == 'token' ? 'selected ' : '' }}value="token">Token Amount</option>
                                                    @foreach($pm_gateways as $pmg => $pmval)
                                                    @if(get_setting('pmc_active_'.$pmg) == 1)
                                                    <option {{ token('sales_cap') == $pmg ? 'selected ' : '' }}value="{{ $pmg }}">{{ $pmval.(($pmg==base_currency()) ? ' (Based)' : '') }}</option>
                                                    @endif
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="gaps-1x"></div>
                                <div class="d-flex">
                                    <button class="btn btn-primary save-disabled" type="submit" disabled><i class="ti ti-reload"></i><span>Update</span></button>
                                </div>
                            </form>
                        </div>
                        <div class="sap sap-gap"></div>
                        <div class="card-text ico-setting setting-ico-userpanel">
                            <h4 class="card-title-md text-primary">User Panel Settings</h4>
                            <p>Manage your User/Investor panel setting for your application.</p>
                            <div class="gaps-1x"></div>
                            <form action="{{ route('admin.ajax.stages.settings.update') }}" method="POST" id="upanel_setting_form" class="validate-modern">
                                @csrf
                                <input type="hidden" name="req_type" value="user_panel">
                                <h5 class="card-title-sm text-secondary">User Dashboard</h5>
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Token Sales Progress</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="user_sales_progress" type="checkbox" {{ gws('user_sales_progress', 1) == 1 ? 'checked' : '' }} id="sales-progress-hide">
                                                <label for="sales-progress-hide">Show</label>
                                            </div>
                                            <span class="input-note">Whether show or hide the <strong>'Token Sales Progress'</strong> in User Panel.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Countdown in Sales Progress</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="opt_count_hide" type="checkbox" {{ gws('opt_count_hide', 0) == 1 ? 'checked' : '' }} id="user-opt-hide">
                                                <label for="user-opt-hide">Hide</label>
                                            </div>
                                            <span class="input-note">Whether hide or show the <strong>token sales countdown options</strong> in User Panel.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Hide Welcome Block Image</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="welcome_img_hide" type="checkbox" {{ gws('welcome_img_hide') == 1 ? 'checked' : '' }} id="welcome-img-hide">
                                                <label for="welcome-img-hide">Hide</label>
                                            </div>
                                            <span class="input-note">Whether hide or show the <strong>Image</strong> from 'Welcome block' in User Panel.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Hide KYC Application</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="kyc_opt_hide" type="checkbox" {{ gws('kyc_opt_hide') == 1 ? 'checked' : '' }} id="user-kyc-hide">
                                                <label for="user-kyc-hide">Hide</label>
                                            </div>
                                            <span class="input-note">Whether hide or show the <strong>'KYC Application'</strong> in User Panel.</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Display Contribution In</label>
                                            <div class="row guttar-15px">
                                                <div class="col-6">
                                                    <div class="input-wrap">
                                                        <select class="select select-block select-bordered" name="user_in_cur1">
                                                            @foreach($pm_gateways as $cur => $name)
                                                            @if(get_setting('pmc_active_'.$cur) == 1 && $cur!=base_currency())
                                                            <option {{ gws('user_in_cur1') == $cur ? 'selected ' : '' }}value="{{ $cur }}">{{ strtoupper($cur) }}</option>
                                                            @endif
                                                            @endforeach
                                                            <option {{ gws('user_in_cur1') == 'hide' ? 'selected ' : '' }}value="hide">Hide</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="input-wrap">
                                                        <select class="select select-block select-bordered" name="user_in_cur2">
                                                            @foreach($pm_gateways as $cur => $name)
                                                            @if(get_setting('pmc_active_'.$cur) == 1 && $cur!=base_currency())
                                                            <option {{ gws('user_in_cur2') == $cur ? 'selected ' : '' }}value="{{ $cur }}">{{ strtoupper($cur) }}</option>
                                                            @endif
                                                            @endforeach
                                                            <option {{ gws('user_in_cur2') == 'hide' ? 'selected ' : '' }}value="hide">Hide</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                            <span class="input-note">Select two currencies which will show on balance card for <strong>'Contribution in'</strong>.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">My Token Page</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="user_mytoken_page" type="checkbox" {{ gws('user_mytoken_page') == 1 ? 'checked' : '' }} id="show-mytoken-page">
                                                <label for="show-mytoken-page">Enable</label>
                                            </div>
                                            <span class="input-note">Whether enable or disable the <strong>'My Token'</strong> page from User Panel.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-3 col-sm-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Stage Wise Overview</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="user_mytoken_stage" type="checkbox" {{ gws('user_mytoken_stage') == 1 ? 'checked' : '' }} id="show-stage-overview">
                                                <label for="show-stage-overview">Show</label>
                                            </div>
                                            <span class="input-note">Whether show or hide the stage wise purchase overview on <strong>'My Token'</strong> page.</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="gaps-2x"></div>
                                <h5 class="card-title-sm text-secondary">Receiving Wallet for User Profile</h5>
                                <p class="wide-lg">You may need your user/investor wallet address so you can send token/smart contract to them. You can specify one or multiple or define your own name to ask your user/investor to provide address. If they provide then you can get from each user details.</p>
                                <div class="gaps-1x"></div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Supported Wallet</label>
                                            <div class="input-wrap">
                                                <select  name="token_wallet_opt[]" class="select select-block select-bordered" value="" data-placeholder="Select Options" multiple="multiple">
                                                    @foreach($supported_wallets as $name => $wallet)
                                                    <option {{in_array($name, $wallet_opt )? 'selected' : ''}} value="{{ $name }}">{{ $wallet }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                            <span class="input-note">Choose one or multiple wallet name.</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Custom Wallet</label>
                                            <div class="row">
                                                <div class="col-6">
                                                    <div class="input-wrap">
                                                        <input class="input-bordered" placeholder="wallet-name" type="text" name = "token_wallet_custom[]" value="{{ (!empty($custom['cw_name']) ? $custom['cw_name'] : '') }}">
                                                    </div>
                                                </div>
                                                <div class="col-6">
                                                    <div class="input-wrap">
                                                        <input class="input-bordered" placeholder="Wallet Label" type="text" name="token_wallet_custom[]" value="{{ (!empty($custom['cw_text']) ? $custom['cw_text'] : '') }}">
                                                    </div>
                                                </div>
                                            </div>
                                            <span class="input-note">You can specify any custom wallet name.</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Note for Wallet</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="text" name="token_wallet_note" value="{{ get_setting('token_wallet_note')}}">
                                            </div>
                                            <span class="input-note">The note will show under the wallet address input field.</span>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-lg-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Before Purchase Alert</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="token_wallet_req" type="checkbox" {{ get_setting('token_wallet_req')==1 ? 'checked ' : '' }}id="before-purchase-alert">
                                                <label for="before-purchase-alert"><span>Hide</span><span class="over">Show</span></label>
                                            </div>
                                            <div class="input-note">Promote 'enter wallet address before buy' on buy token page.</div>
                                        </div>
                                    </div>
                                    <div class="col-sm-12 col-lg-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Wallet Required Before Purchase</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="token_before_wallet" type="checkbox" {{ get_setting('token_before_wallet')==1 ? 'checked ' : '' }}id="token-before-wallet">
                                                <label for="token-before-wallet"><span>Disable</span><span class="over">Enable</span></label>
                                            </div>
                                            <div class="input-note">Wallet address is required before purchase tokens.</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="gaps-1x"></div>
                                <div class="d-flex">
                                    <button class="btn btn-primary save-disabled" type="submit" disabled><i class="ti ti-reload"></i><span>Update</span></button>
                                </div>
                            </form>
                        </div>
                        <div class="sap sap-gap"></div>
                        <div class="card-text ico-setting setting-smart-contract">
                            <h4 class="card-title-md text-primary">Smart Contract Settings</h4>
                            <p>Configure your smart contract integration for automatic token distribution.</p>
                            <div class="gaps-1x"></div>
                            <form action="{{ route('admin.ajax.stages.settings.update') }}" method="POST" id="smart_contract_form" class="validate-modern">
                                @csrf
                                <input type="hidden" name="req_type" value="smart_contract">

                                <h5 class="card-title-sm text-secondary">Contract Configuration</h5>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Proxy Contract Address</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="text" name="token_contract_address" value="{{ get_setting('token_contract_address', '******************************************') }}" placeholder="0x...">
                                            </div>
                                            <span class="input-note">Enter your proxy contract address (EIP-1967).</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Implementation Contract Address</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="text" name="token_contract_implementation_address" value="{{ get_setting('token_contract_implementation_address', '0x779042c47025872616084c237062cccf99a29947') }}" placeholder="0x...">
                                            </div>
                                            <span class="input-note">Enter the implementation contract address.</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-3">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Proxy Type</label>
                                            <div class="input-wrap">
                                                <select class="select select-block select-bordered" name="token_contract_proxy_type">
                                                    <option {{ get_setting('token_contract_proxy_type') == 'EIP1967' ? 'selected' : '' }} value="EIP1967">EIP-1967 Transparent</option>
                                                    <option {{ get_setting('token_contract_proxy_type') == 'OpenZeppelin' ? 'selected' : '' }} value="OpenZeppelin">OpenZeppelin Proxy</option>
                                                    <option {{ get_setting('token_contract_proxy_type') == 'Custom' ? 'selected' : '' }} value="Custom">Custom Proxy</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Is Proxy Contract</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="token_contract_is_proxy" type="checkbox" {{ get_setting('token_contract_is_proxy') == 1 ? 'checked' : '' }} id="is-proxy">
                                                <label for="is-proxy">Enable</label>
                                            </div>
                                            <span class="input-note">Enable if using proxy pattern.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Network</label>
                                            <div class="input-wrap">
                                                <select class="select select-block select-bordered" name="token_contract_network">
                                                    <option {{ get_setting('token_contract_network') == 'bsc' ? 'selected' : '' }} value="bsc">BSC Mainnet</option>
                                                    <option {{ get_setting('token_contract_network') == 'ethereum' ? 'selected' : '' }} value="ethereum">Ethereum Mainnet</option>
                                                    <option {{ get_setting('token_contract_network') == 'polygon' ? 'selected' : '' }} value="polygon">Polygon Mainnet</option>
                                                    <option {{ get_setting('token_contract_network') == 'goerli' ? 'selected' : '' }} value="goerli">Goerli Testnet</option>
                                                    <option {{ get_setting('token_contract_network') == 'sepolia' ? 'selected' : '' }} value="sepolia">Sepolia Testnet</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Contract Decimals</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="number" name="token_contract_decimals" value="{{ get_setting('token_contract_decimals', 18) }}" min="0" max="18">
                                            </div>
                                            <span class="input-note">Token decimals (usually 18).</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Web3 RPC URL</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="url" name="web3_rpc_url" value="{{ get_setting('web3_rpc_url') }}" placeholder="https://mainnet.infura.io/v3/YOUR_PROJECT_ID">
                                            </div>
                                            <span class="input-note">Blockchain RPC endpoint (Infura, Alchemy, etc.).</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Gas Limit</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="number" name="token_contract_gas_limit" value="{{ get_setting('token_contract_gas_limit', 100000) }}" min="21000">
                                            </div>
                                            <span class="input-note">Gas limit for transactions.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Gas Price (Gwei)</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="number" name="token_contract_gas_price" value="{{ get_setting('token_contract_gas_price', 20) }}" min="1">
                                            </div>
                                            <span class="input-note">Gas price in Gwei.</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="gaps-2x"></div>
                                <h5 class="card-title-sm text-secondary">Contract Features</h5>
                                <div class="row">
                                    <div class="col-lg-3">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Enable Smart Contract</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="token_contract_enabled" type="checkbox" {{ get_setting('token_contract_enabled') == 1 ? 'checked' : '' }} id="contract-enabled">
                                                <label for="contract-enabled">Enable</label>
                                            </div>
                                            <span class="input-note">Enable smart contract integration.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-3">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Auto Token Distribution</label>
                                            <div class="input-wrap input-wrap-switch">
                                                <input class="input-switch" name="token_contract_auto_distribute" type="checkbox" {{ get_setting('token_contract_auto_distribute') == 1 ? 'checked' : '' }} id="auto-distribute">
                                                <label for="auto-distribute">Enable</label>
                                            </div>
                                            <span class="input-note">Automatically distribute tokens after payment confirmation.</span>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="input-item input-with-label">
                                            <label class="input-item-label">Contract Owner Address</label>
                                            <div class="input-wrap">
                                                <input class="input-bordered" type="text" name="token_contract_owner_address" value="{{ get_setting('token_contract_owner_address') }}" placeholder="0x...">
                                            </div>
                                            <span class="input-note">Contract owner address for token distribution.</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="gaps-1x"></div>
                                <div class="d-flex">
                                    <button class="btn btn-primary save-disabled" type="submit" disabled><i class="ti ti-reload"></i><span>Update Contract Settings</span></button>
                                    <button class="btn btn-info ml-3" type="button" id="test-contract-connection"><i class="ti ti-pulse"></i><span>Test Connection</span></button>
                                </div>

                                {{-- Smart Contract Management Buttons --}}
                                @if(get_setting('token_contract_enabled', 0))
                                <div class="gaps-2x"></div>
                                <div class="card card-bordered">
                                    <div class="card-inner">
                                        <h6 class="card-title">Smart Contract Management</h6>
                                        <p class="card-text">Execute smart contract functions including mint, burn, transfer, and advanced features.</p>

                                        <div class="row g-3">
                                            <div class="col-sm-6 col-lg-4">
                                                <button class="btn btn-success btn-block" type="button" id="smart-contract-mint">
                                                    <i class="ti ti-plus-circle"></i><span>Mint Tokens</span>
                                                </button>
                                            </div>
                                            <div class="col-sm-6 col-lg-4">
                                                <button class="btn btn-danger btn-block" type="button" id="smart-contract-burn">
                                                    <i class="ti ti-trash"></i><span>Burn Tokens</span>
                                                </button>
                                            </div>
                                            <div class="col-sm-6 col-lg-4">
                                                <button class="btn btn-primary btn-block" type="button" id="smart-contract-transfer">
                                                    <i class="ti ti-send"></i><span>Transfer Tokens</span>
                                                </button>
                                            </div>
                                            <div class="col-sm-6 col-lg-4">
                                                <button class="btn btn-warning btn-block" type="button" id="smart-contract-pause">
                                                    <i class="ti ti-control-pause"></i><span>Pause Contract</span>
                                                </button>
                                            </div>
                                            <div class="col-sm-6 col-lg-4">
                                                <button class="btn btn-success btn-block" type="button" id="smart-contract-unpause">
                                                    <i class="ti ti-control-play"></i><span>Unpause Contract</span>
                                                </button>
                                            </div>
                                            <div class="col-sm-6 col-lg-4">
                                                <button class="btn btn-info btn-block" type="button" onclick="window.open('https://bscscan.com/address/{{ get_setting('token_contract_address') }}', '_blank')">
                                                    <i class="ti ti-external-link"></i><span>View on BscScan</span>
                                                </button>
                                            </div>
                                        </div>

                                        <div class="gaps-1x"></div>
                                        <div class="alert alert-info">
                                            <strong>Current Mode:</strong> Simulation (Safe Testing)<br>
                                            <strong>To Enable Live Functions:</strong> Add CONTRACT_PRIVATE_KEY and CONTRACT_OWNER_ADDRESS to .env file<br>
                                            <strong>Network:</strong> BSC Mainnet | <strong>Proxy:</strong> {{ substr(get_setting('token_contract_address'), 0, 10) }}... | <strong>Implementation:</strong> {{ substr(get_setting('token_contract_implementation_address'), 0, 10) }}...
                                        </div>
                                    </div>
                                </div>
                                @endif
                            </form>
                        </div>

                        {{-- @dd($modules) --}}
                        @if(isset($modules) && !empty($modules))
                        @foreach($modules as $opt)
                        @if(!empty($opt->view))
                            <div class="sap sap-gap"></div>
                            <div class="card-text ico-setting setting-ico-userpanel">
                                @includeIf($opt->view, $opt->variables)
                            </div>
                        @endif
                        @endforeach
                        @endif
                    </div>{{-- .card-innr --}}
                </div>{{-- .card --}}

            </div>{{-- .col --}}
        </div>{{-- .container --}}
    </div>{{-- .container --}}
</div>{{-- .page-content --}}
@endsection

@push('footer')
<script>
$(document).ready(function() {
    // Test smart contract connection
    $('#test-contract-connection').on('click', function(e) {
        e.preventDefault();
        var btn = $(this);
        var originalText = btn.html();

        btn.html('<i class="ti ti-reload fa-spin"></i><span>Testing...</span>').prop('disabled', true);

        $.ajax({
            url: '{{ route("admin.ajax.test.contract") }}',
            type: 'POST',
            data: {
                _token: '{{ csrf_token() }}',
                contract_address: $('input[name="token_contract_address"]').val(),
                implementation_address: $('input[name="token_contract_implementation_address"]').val(),
                network: $('select[name="token_contract_network"]').val(),
                rpc_url: $('input[name="web3_rpc_url"]').val(),
                is_proxy: $('input[name="token_contract_is_proxy"]').is(':checked')
            },
            success: function(response) {
                if (response.status === 'success') {
                    show_toast('success', 'Contract connection successful!<br>' + response.message);
                } else {
                    show_toast('error', 'Contract connection failed!<br>' + response.message);
                }
            },
            error: function(xhr) {
                var errorMsg = 'Connection test failed!';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg += '<br>' + xhr.responseJSON.message;
                }
                show_toast('error', errorMsg);
            },
            complete: function() {
                btn.html(originalText).prop('disabled', false);
            }
        });
    });

    // Smart Contract Management Functions
    $('#smart-contract-transfer').on('click', function() {
        var recipient = prompt('Enter recipient address:');
        var amount = prompt('Enter amount:');
        if (recipient && amount) {
            executeSmartContractFunction('transfer', {recipient: recipient, amount: amount});
        }
    });

    $('#smart-contract-mint').on('click', function() {
        var recipient = prompt('Enter recipient address:');
        var amount = prompt('Enter amount to mint:');
        if (recipient && amount) {
            executeSmartContractFunction('mint', {recipient: recipient, amount: amount});
        }
    });

    $('#smart-contract-burn').on('click', function() {
        var amount = prompt('Enter amount to burn:');
        if (amount) {
            executeSmartContractFunction('burn', {amount: amount});
        }
    });

    $('#smart-contract-pause').on('click', function() {
        if (confirm('Are you sure you want to pause the contract?')) {
            executeSmartContractFunction('pause', {});
        }
    });

    $('#smart-contract-unpause').on('click', function() {
        if (confirm('Are you sure you want to unpause the contract?')) {
            executeSmartContractFunction('unpause', {});
        }
    });

    function executeSmartContractFunction(method, data) {
        // Show simulation message
        var message = 'Smart Contract Function Executed:\n\n';
        message += 'Method: ' + method + '\n';
        message += 'Data: ' + JSON.stringify(data, null, 2) + '\n\n';
        message += 'Status: SIMULATED (Safe Mode)\n\n';
        message += 'To enable live execution:\n';
        message += '1. Add CONTRACT_PRIVATE_KEY to .env\n';
        message += '2. Add CONTRACT_OWNER_ADDRESS to .env\n';
        message += '3. Functions will execute on BSC blockchain';

        alert(message);

        // Log to console for debugging
        console.log('Smart Contract Function:', method, data);
    }
});
</script>
@endpush
