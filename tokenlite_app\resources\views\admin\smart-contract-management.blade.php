@extends('layouts.admin')
@section('title', 'Smart Contract Management')

@section('content')
<div class="container-fluid">
    <div class="nk-content-inner">
        <div class="nk-content-body">
            <div class="nk-block-head nk-block-head-sm">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h3 class="nk-block-title page-title">Smart Contract Management</h3>
                        <div class="nk-block-des text-soft">
                            <p>Manage all smart contract functions including mint, burn, transfer, and advanced features.</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contract Status Card -->
            <div class="row g-gs">
                <div class="col-lg-12">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <div class="card-title-group align-start mb-3">
                                <div class="card-title">
                                    <h6 class="title">Contract Status</h6>
                                </div>
                                <div class="card-tools">
                                    <button class="btn btn-outline-primary btn-sm" id="refresh-status">
                                        <em class="icon ni ni-reload"></em> Refresh
                                    </button>
                                </div>
                            </div>
                            <div class="row g-3">
                                <div class="col-sm-6 col-lg-3">
                                    <div class="overview-wrap">
                                        <div class="overview-number">
                                            <div class="overview-panel-s1 text-primary">
                                                <span class="overview-icon">
                                                    <em class="icon ni ni-link"></em>
                                                </span>
                                                <span class="overview-label">Proxy Contract</span>
                                                <span class="overview-val">{{ substr(get_setting('token_contract_address'), 0, 10) }}...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-3">
                                    <div class="overview-wrap">
                                        <div class="overview-number">
                                            <div class="overview-panel-s1 text-info">
                                                <span class="overview-icon">
                                                    <em class="icon ni ni-globe"></em>
                                                </span>
                                                <span class="overview-label">Network</span>
                                                <span class="overview-val">{{ strtoupper(get_setting('token_contract_network', 'BSC')) }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-3">
                                    <div class="overview-wrap">
                                        <div class="overview-number">
                                            <div class="overview-panel-s1 text-success">
                                                <span class="overview-icon">
                                                    <em class="icon ni ni-check-circle"></em>
                                                </span>
                                                <span class="overview-label">Status</span>
                                                <span class="overview-val">{{ get_setting('token_contract_enabled') ? 'Active' : 'Disabled' }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6 col-lg-3">
                                    <div class="overview-wrap">
                                        <div class="overview-number">
                                            <div class="overview-panel-s1 text-warning">
                                                <span class="overview-icon">
                                                    <em class="icon ni ni-setting"></em>
                                                </span>
                                                <span class="overview-label">Proxy Type</span>
                                                <span class="overview-val">{{ get_setting('token_contract_proxy_type', 'EIP1967') }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Smart Contract Functions -->
            <div class="row g-gs mt-4">
                <!-- Token Transfer -->
                <div class="col-lg-6">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <h6 class="card-title">Token Transfer</h6>
                            <form id="transfer-form">
                                <div class="form-group">
                                    <label class="form-label">Recipient Address</label>
                                    <input type="text" class="form-control" name="recipient" placeholder="0x...">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Amount</label>
                                    <input type="number" class="form-control" name="amount" placeholder="0.00">
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <em class="icon ni ni-send"></em> Transfer Tokens
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Token Mint -->
                <div class="col-lg-6">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <h6 class="card-title">Mint Tokens</h6>
                            <form id="mint-form">
                                <div class="form-group">
                                    <label class="form-label">Recipient Address</label>
                                    <input type="text" class="form-control" name="recipient" placeholder="0x...">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Amount to Mint</label>
                                    <input type="number" class="form-control" name="amount" placeholder="0.00">
                                </div>
                                <button type="submit" class="btn btn-success">
                                    <em class="icon ni ni-plus-circle"></em> Mint Tokens
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Token Burn -->
                <div class="col-lg-6">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <h6 class="card-title">Burn Tokens</h6>
                            <form id="burn-form">
                                <div class="form-group">
                                    <label class="form-label">Amount to Burn</label>
                                    <input type="number" class="form-control" name="amount" placeholder="0.00">
                                </div>
                                <div class="form-group">
                                    <label class="form-label">Burn From Address (Optional)</label>
                                    <input type="text" class="form-control" name="from_address" placeholder="0x... (leave empty to burn from owner)">
                                </div>
                                <button type="submit" class="btn btn-danger">
                                    <em class="icon ni ni-trash"></em> Burn Tokens
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Batch Transfer -->
                <div class="col-lg-6">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <h6 class="card-title">Batch Transfer</h6>
                            <form id="batch-transfer-form">
                                <div class="form-group">
                                    <label class="form-label">Recipients & Amounts (JSON)</label>
                                    <textarea class="form-control" name="batch_data" rows="4" placeholder='[{"address":"0x...","amount":"100"},{"address":"0x...","amount":"200"}]'></textarea>
                                </div>
                                <button type="submit" class="btn btn-info">
                                    <em class="icon ni ni-share"></em> Batch Transfer
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Contract Controls -->
                <div class="col-lg-6">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <h6 class="card-title">Contract Controls</h6>
                            <div class="d-flex flex-wrap gap-2">
                                <button class="btn btn-warning" id="pause-contract">
                                    <em class="icon ni ni-pause"></em> Pause Contract
                                </button>
                                <button class="btn btn-success" id="unpause-contract">
                                    <em class="icon ni ni-play"></em> Unpause Contract
                                </button>
                                <button class="btn btn-info" id="check-paused">
                                    <em class="icon ni ni-info"></em> Check Status
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Blacklist Management -->
                <div class="col-lg-6">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <h6 class="card-title">Blacklist Management</h6>
                            <form id="blacklist-form">
                                <div class="form-group">
                                    <label class="form-label">Address</label>
                                    <input type="text" class="form-control" name="address" placeholder="0x...">
                                </div>
                                <div class="d-flex gap-2">
                                    <button type="button" class="btn btn-danger" id="add-blacklist">
                                        <em class="icon ni ni-cross-circle"></em> Blacklist
                                    </button>
                                    <button type="button" class="btn btn-success" id="remove-blacklist">
                                        <em class="icon ni ni-check-circle"></em> Unblacklist
                                    </button>
                                    <button type="button" class="btn btn-info" id="check-blacklist">
                                        <em class="icon ni ni-eye"></em> Check Status
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Transaction History -->
            <div class="row g-gs mt-4">
                <div class="col-lg-12">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <div class="card-title-group">
                                <div class="card-title">
                                    <h6 class="title">Recent Smart Contract Transactions</h6>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Action</th>
                                            <th>Method</th>
                                            <th>Address</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Hash</th>
                                        </tr>
                                    </thead>
                                    <tbody id="transaction-history">
                                        <!-- Dynamic content -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('footer')
<script>
$(document).ready(function() {
    // CSRF token for all AJAX requests
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });

    // Transfer Form
    $('#transfer-form').on('submit', function(e) {
        e.preventDefault();
        executeContractFunction('transfer', $(this).serialize());
    });

    // Mint Form
    $('#mint-form').on('submit', function(e) {
        e.preventDefault();
        executeContractFunction('mint', $(this).serialize());
    });

    // Burn Form
    $('#burn-form').on('submit', function(e) {
        e.preventDefault();
        executeContractFunction('burn', $(this).serialize());
    });

    // Batch Transfer Form
    $('#batch-transfer-form').on('submit', function(e) {
        e.preventDefault();
        executeContractFunction('batchTransfer', $(this).serialize());
    });

    // Contract Controls
    $('#pause-contract').on('click', function() {
        executeContractFunction('pause', {});
    });

    $('#unpause-contract').on('click', function() {
        executeContractFunction('unpause', {});
    });

    $('#check-paused').on('click', function() {
        executeContractFunction('paused', {});
    });

    // Blacklist Management
    $('#add-blacklist').on('click', function() {
        var address = $('input[name="address"]').val();
        executeContractFunction('blacklist', {address: address});
    });

    $('#remove-blacklist').on('click', function() {
        var address = $('input[name="address"]').val();
        executeContractFunction('unBlacklist', {address: address});
    });

    $('#check-blacklist').on('click', function() {
        var address = $('input[name="address"]').val();
        executeContractFunction('isBlacklisted', {address: address});
    });

    // Execute Contract Function
    function executeContractFunction(method, data) {
        $.ajax({
            url: '{{ route("admin.smart-contract.execute") }}',
            type: 'POST',
            data: {
                method: method,
                parameters: data
            },
            beforeSend: function() {
                NioApp.Toast('Processing...', 'info');
            },
            success: function(response) {
                if (response.status === 'success') {
                    NioApp.Toast(response.message, 'success');
                    loadTransactionHistory();
                } else {
                    NioApp.Toast(response.message, 'error');
                }
            },
            error: function(xhr) {
                NioApp.Toast('Error: ' + xhr.responseJSON.message, 'error');
            }
        });
    }

    // Load Transaction History
    function loadTransactionHistory() {
        $.ajax({
            url: '{{ route("admin.smart-contract.history") }}',
            type: 'GET',
            success: function(response) {
                $('#transaction-history').html(response.html);
            }
        });
    }

    // Initial load
    loadTransactionHistory();
});
</script>
@endpush
